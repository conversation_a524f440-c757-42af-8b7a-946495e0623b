import React, { useState, useEffect, FC, useMemo } from 'react';
import { map, filter, isEmpty, forEach } from 'lodash';
import { getGuardProductInstance } from '@src/service/api/broadcast-drawer';
import { useToggle } from '@src/hooks/common';
import originStore from '@src/origin-store/store';
import { EENDTYPE } from '@src/constants';
import { Tag, message, Transfer, Table, TagSearchBox } from '@tencent/tea-component';
import { AttributeValue } from '@tencent/tea-component/src/tagsearchbox/AttributeSelect';
import { t, Trans, Slot } from '@tea/app/i18n';
import './index.less';
interface IResourceSupplePanelProps {
  guardId: number;
  resourceDetail: any;
  productLimitMap: any;
  onInsListLoad: Function
  editable: boolean
  onSourceListChange: Function
}
const { selectable, removeable, scrollable, autotip } = Table.addons;

const columns = [
  {
    key: 'AppId',
    header: 'APPID',
  },
  {
    key: 'InstanceId',
    header: t('实例ID/实例名'),
    width: '50%',
    render: (item) => {
      const { InstanceId, InstanceName } = item;
      return (<>
                {InstanceId && <><Tag theme="primary">{InstanceId}</Tag><br /></>}
                {InstanceName && <Tag>{InstanceName}</Tag>}
            </>);
    },
  },
  {
    key: 'Region',
    header: t('地域'),
  },
];


const attributes: Array<AttributeValue> = [
  {
    type: 'input',
    key: 'insID',
    name: t('实例ID'),
  },
  {
    type: 'input',
    key: 'insName',
    name: t('实例名'),
  },
];

function SourceTable({ dataSource, targetKeys, onChange, loading, editable }: any) {
  return (
    <Table
      records={dataSource}
      recordKey="InstanceId"
      rowDisabled={() => !editable}
      columns={columns}
      addons={[
        scrollable({
          maxHeight: 310,
        }),
        selectable({
          value: targetKeys,
          onChange,
          rowSelect: true,
        }),
        autotip({
          isLoading: loading,
        }),
      ]}
    />
  );
}

function TargetTable({ dataSource, onRemove, loading, editable }: any) {
  return (
    <Table
      records={dataSource}
      recordKey="InstanceId"
      rowDisabled={() => !editable}
      columns={columns}
      addons={[
        removeable({ onRemove }),
        autotip({
          isLoading: loading,
        }),
      ]}
    />
  );
}

const ResourcePicker: FC<IResourceSupplePanelProps> = ({
  guardId,
  editable,
  resourceDetail,
  productLimitMap,
  onInsListLoad,
  onSourceListChange,
}: IResourceSupplePanelProps) => {
  const queryParams = new URLSearchParams(location.search);
  const appId = +queryParams.get('appid');
  const {
    graphApi,
  }  = originStore.getState().guard;
  const type = {
    CONSOLE: EENDTYPE.CUSTOMER,
    ISA: EENDTYPE.OPERATOR,
  }[graphApi.env];
  const { Product, ResourceIds } = resourceDetail;
  const [isLoading, startLoad, endLoad] = useToggle(false);
  const [tagSelectBoxValue, setTagSelectBoxValue] = useState([]);
  const [targetKeys, setTargetKeys] = useState(map(ResourceIds, ({ InstanceId }) => InstanceId));
  const [instanceList, setInstanceList] = useState([]);

  // 获取护航信息
  const getGuardSheet = async () => {
    startLoad();
    const filters = [
      { Name: 'guard_id', Values: [`${guardId}`] },
      { Name: 'product', Values: [Product] },
    ];
    getGuardProductInstance({
      Filters: filters.filter((i) => {
        if (i.Values.length) return i;
      }),
      ...(type === EENDTYPE.OPERATOR && { AppId: appId }),
    })
      .then((res: any) => {
        if (res.Error) {
          const msg = res.Error.Message;
          message.error({ content: msg });
          endLoad();
          return;
        }
        setInstanceList(res.Instance);
        onInsListLoad(res.Instance, Product);
        endLoad();
      })
      .catch((err) => {
        const msg = err.msg || err.toString() || t('未知错误');
        message.error({ content: msg });
      });
  };

  const handleSearchTagChange = (tags) => {
    setTagSelectBoxValue(tags);
  };

  const tagSearchBoxProps = {
    minWidth: '100%',
    attributes,
    value: tagSelectBoxValue,
    hideHelp: true,
    tips: t('支持批量搜索，多个关键词用竖线"|"分割'),
    onChange: handleSearchTagChange,
  };

  // 搜索
  const filterList = useMemo(() => {
    if (isEmpty(tagSelectBoxValue)) return instanceList;
    const keyWords = [];
    forEach(tagSelectBoxValue, (tag) => {
      forEach(tag.values, (tagValues) => {
        keyWords.push({ type: tag.attr?.key, name: tagValues.name });
      });
    });
    return filter(instanceList, (ins) => {
      for (const keyWord of keyWords) {
        if (ins.InstanceId.includes(keyWord.name) && keyWord.type === 'insID') return true;
        if (ins.InstanceName.includes(keyWord.name) && keyWord.type === 'insName') return true;
      }
      return false;
    });
  }, [tagSelectBoxValue, instanceList]);

  useEffect(() => {
    getGuardSheet();
  }, [Product]);

  useEffect(() => {
    const targetKeys = map(ResourceIds, ({ InstanceId }) => InstanceId);
    setTargetKeys(targetKeys);
  }, [ResourceIds]);

  useEffect(() => {
    onSourceListChange(filter(instanceList, i => targetKeys.includes(`${i.InstanceId}`)), Product);
  }, [targetKeys, instanceList]);

  const handleSourceChange = (keys) => {
    const limit = productLimitMap[Product];

    // 如果没有设置上限，直接设置
    if (!limit) {
      setTargetKeys(keys);
      return;
    }

    // 如果选择的数量超过上限，只保留前N个，并提示
    if (keys.length > limit) {
      const limitedKeys = keys.slice(0, limit);
      setTargetKeys(limitedKeys);
      message.warning({
        content: t('资源已选择到上限，最多只能选择{{limit}}个资源', { limit }),
      });
      return;
    }

    // 正常情况，直接设置
    setTargetKeys(keys);
  };

  return (
    <div className='resource-picker-box'>
      <Transfer
        leftCell={
          <Transfer.Cell
            scrollable={false}
            title={
              <div style={{ marginTop: 6 }}>
                <span style={{ fontSize: '14px', fontWeight: 'bold' }}><Trans>
                  <span style={{ color: 'red', marginRight: 5 }}>*</span>
                  请选择重点实例，播报上限：<Slot content={productLimitMap[Product]} />
                </Trans></span>
              </div>
            }
            header={
              <TagSearchBox {...tagSearchBoxProps}/>
            }
          >
            <SourceTable
              dataSource={filterList}
              targetKeys={targetKeys}
              onChange={keys => handleSourceChange(keys)}
              loading={isLoading}
              editable={editable}
            />
          </Transfer.Cell>
        }
        rightCell={
          <Transfer.Cell title={t('已选择 ({{attr0}})', { attr0: targetKeys.length })}>
            <TargetTable
              dataSource={filter(instanceList, i => targetKeys.includes(`${i.InstanceId}`))}
              onRemove={key => setTargetKeys(targetKeys.filter(i => i !== key))}
              loading={isLoading}
              editable={editable}
            />
          </Transfer.Cell>
        }
      />
    </div>
  );
};
export default ResourcePicker;
