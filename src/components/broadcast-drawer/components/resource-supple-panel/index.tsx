import React, { useState, useEffect, FC, useMemo, useImperativeHandle } from 'react';
import './index.less';
import { isEmpty, forEach, clone, map, pick, toPairs, reduce, isEqual, orderBy, filter, includes, set } from 'lodash';
import ResourcePicker from '../resource-picker';
import originStore from '@src/origin-store/store';
import { EENDTYPE } from '@src/constants';
import ResourceFlow from '../resource-flow';
import { Collapse, Status, message, Icon } from '@tencent/tea-component';
import { ModifyBroadcastResources, describeBroadcastLimits } from '@src/service/api/broadcast-drawer';
import { t } from '@tea/app/i18n';
interface IResourceSupplePanelProps {
  guardId: number;
  broadcastId: number
  resourcePending: any
  editable: boolean
  operator: string
  onInfoChange: Function
  reload: Function
  ref: any // 加上这个只是为了避免ts误报
  online?: any
}

const allProductInsList = {};

// eslint-disable-next-line react/display-name
const ResourceSupplePanel: FC<IResourceSupplePanelProps> = React.forwardRef((
  { guardId,
    broadcastId,
    resourcePending,
    editable,
    operator,
    onInfoChange,
    reload,
    online,
  }: IResourceSupplePanelProps,
  ref
) => {
  const queryParams = new URLSearchParams(location.search);
  const appId = +queryParams.get('appid');
  const {
    graphApi,
  }  = originStore.getState().guard;
  const type = {
    CONSOLE: EENDTYPE.CUSTOMER,
    ISA: EENDTYPE.OPERATOR,
  }[graphApi.env];
  const [resourceInfoList, setResourceInfoList] = useState({});
  const [productToLimitMap, setProductToLimitMap] = useState({});

  const handleSourceListChange = (targetKeys, Product) => {
    const temp = clone(resourceInfoList);
    temp[Product] = targetKeys;
    setResourceInfoList(temp);
  };

  useEffect(() => {
    const resources = reduce(toPairs(resourceInfoList), (ret, [key, value]) => {
      const resourceIds = map(value as Array<{ InstanceId: string }>, ({ InstanceId }) => ({ InstanceId }));
      ret.push({
        Product: key,
        ResourceIds: orderBy(resourceIds, ['InstanceId'], ['asc']),
      });
      return ret;
    }, []);
    const initResource = map(resourcePending, ({ Product, ResourceIds }) => {
      if (Product === 'stream') {
        const resourceIds = map(ResourceIds, ({ InstanceId }) => ({ InstanceId: InstanceId.split('/')[1] }));
        return { Product, ResourceIds: resourceIds };
      }

      return {
        Product,
        ResourceIds: orderBy(ResourceIds, ['InstanceId'], ['asc']),
      };
    });
    onInfoChange(isEqual(resources, initResource));
  }, [resourcePending, resourceInfoList]);

  useEffect(() => {
    const initResourceInfo = {};
    forEach(resourcePending, (item) => {
      if (item.Product === 'stream') {
        initResourceInfo[item.Product] = map(item.ResourceIds, ({ InstanceId }) => {
          const resourceInfo = InstanceId.split('/');
          if (resourceInfo[0]) {
            return {
              AppId: +resourceInfo[0],
              Product: item.Product,
              InstanceId: resourceInfo[1],
            };
          }
        });
      } else {
        initResourceInfo[item.Product] = item.ResourceIds;
      }
    });
    setResourceInfoList(initResourceInfo);
  }, [resourcePending]);

  const handleInsListLoad = (insList, product) => {
    allProductInsList[product] = insList;
  };

  const defaultActiveIds = useMemo(() => (editable ? ['1'] : map(resourcePending, (item, index) => `${index + 1}`)), [editable, resourcePending]);

  const handleResourceInfoSave = async () => {
    if (isEmpty(resourcePending)) {
      return true;
    };
    try {
      // 实例有变化的数据
      const changedResourceInfo = {};
      for (const product in resourceInfoList) {
        const oldInfo = resourcePending.filter(i => i.Product === product)[0]?.ResourceIds || [];
        // 已保存的产品实例
        const oldInstance = oldInfo.map(i => i.InstanceId) || [];
        // 当前的产品实例
        const currentInstance = resourceInfoList[product].map(i => i.InstanceId);
        // 判断实例是否有更改
        if (JSON.stringify(oldInstance) !== JSON.stringify(currentInstance)) {
          changedResourceInfo[product] = resourceInfoList[product];
        }
      }
      const Resources = reduce(toPairs(changedResourceInfo), (ret: any[], [key, value]) => {
        let filterIns: any[];
        if (key === 'stream') {
          filterIns = value as any[];
        } else {
          const targetKey = map(value as Array<{ InstanceId: string }>, ({ InstanceId }) => InstanceId);
          const filterInsList = filter(allProductInsList[key], i => includes(targetKey, i.InstanceId));
          filterIns = filterInsList;
        }
        if (filterIns.length) {
          map(filterIns, (item) => {
            const temp = pick(item, 'AppId', 'Product', 'Region', 'Extra');
            ret.push({ ...temp, ResourceId: item.InstanceId, ResourceName: item.InstanceName });
          });
        } else {
          ret.push({ AppId: 0, Product: key, Region: '', ResourceId: '', ResourceName: '' });
        }
        return ret;
      }, []);
      const res: any = await ModifyBroadcastResources({
        BroadcastId: broadcastId,
        Resources,
        Updater: operator,
        ...(type === EENDTYPE.OPERATOR && { AppId: appId }),
      });
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        reload();
        return false;
      }
      message.success({ content: t('已自动保存') });
      reload();
      return true;
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
      return false;
    }
  };

  const getBroadcastLimits = () => {
    const productList = map(resourcePending, ({ Product }) => Product);
    describeBroadcastLimits({
      Products: productList,
    })
      .then((rs: any) => {
        const productLimitMap = {};
        map(rs?.ProductLimits, (item) => {
          const { Product, Limit } = item;
          set(productLimitMap, Product, Limit);
        });
        setProductToLimitMap(productLimitMap);
      })
      .catch((err) => {
        console.log(err);
      });
  };
  // 将保存方法暴露给父组件
  useImperativeHandle(ref, () => ({
    save: handleResourceInfoSave,
  }));

  useEffect(() => {
    getBroadcastLimits();
  }, []);

  return (
		<div className='intlc-broadcast-resource__inner'>
			<div className='intlc-broadcast__body'>
				{
					isEmpty(resourcePending)
					  ? <Status
							icon="blank"
							size="s"
							title={t('无资源信息需要补充')}
						/>
					  : <>
							<Collapse
								defaultActiveIds={defaultActiveIds}
								iconPosition="right">
								{
									resourcePending.map((item, index) => <div className='intlc-broadcast-resource__item' key={index}>
											<Collapse.Panel
												id={`${index + 1}`}
												title={() => (
													<div className='broadcastProductWrap'>
														<div>{item.Name}</div>
														{online === 2
															&& (<div className='productStatu'>
																<Icon type={item.IsPending ? 'error' : 'success'} />
																{item.IsPending && <span>{t('待补充')}</span>}
															</div>)}
													</div>
												)}
												className="intlc-broadcast-resource__panel"
											>
												{
													item.Product === 'stream'
													  ? <ResourceFlow
															guardId={guardId}
															editable={editable}
															resourceDetail={item}
															onSourceListChange={handleSourceListChange}
														/>
													  : <ResourcePicker
															guardId={guardId}
															editable={editable}
															resourceDetail={item}
                              productLimitMap={productToLimitMap}
															onInsListLoad={
                                (insList, product) => handleInsListLoad(insList, product)
                              }
															onSourceListChange={handleSourceListChange}
														/>
												}
											</Collapse.Panel>
										</div>)
								}
							</Collapse>
							{/* { */}
							{/* 	editable */}
							{/* 	&& <Form.Action style={{ display: 'flex', justifyContent: 'center' }}> */}
							{/* 		<Button */}
							{/* 			type="primary" */}
							{/* 			onClick={handleResourceInfoSave} */}
							{/* 		> */}
							{/* 			保存 */}
							{/* 		</Button> */}
							{/* 	</Form.Action> */}
							{/* } */}
						</>
				}
			</div>
		</div>
  );
});
export default ResourceSupplePanel;
