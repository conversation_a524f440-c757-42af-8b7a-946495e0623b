import React, { useEffect, useState } from 'react';
import { Cha<PERSON><PERSON><PERSON><PERSON>, Chat<PERSON><PERSON><PERSON>, ChatInstructionList } from '@tencent/cloud-chat-ui';
import { Input, Icon, Bubble, message, StatusTip } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
import { describeGuardAISubscribeMetricInfo } from '@src/service/api/broadcast-agent-chat';
import './index.less';

interface Props {
  visible: boolean,
  onClose: () => void,
  inputValue: string,
  setInputValue: (val: string) => void,
}

enum TitleTabsEnum {
  template = 'template'
}


const SubscribeConfigBotDrawer = ({ visible, onClose, inputValue, setInputValue }: Props) => {
  const [titleTabs] = useState([
    { id: TitleTabsEnum.template, label: t('订阅模板') },
  ]);
  const [contentTabs, setContentTabs] = useState([]);
  const [tabsDataMap, setTabsDataMap] = useState({});
  const [searchValue, setSearchValue] = useState('');
  const [list, setList] = useState([]);
  const getDescribeGuardAISubscribeMetricInfo = async () => {
    const res: any = await describeGuardAISubscribeMetricInfo();
    if (res.Error) {
      const msg = res.Error.Message;
      message.error({ content: msg });
      return;
    }
    const temTabs = [];
    const temMap = {};
    const temList = [];
    res?.ProductMetricInfoList?.forEach((item) => {
      temTabs.push({
        id: item.Product,
        label: item.ProductName,
      });
      temMap[item.Product] = item.MetricInfoList;
      item.MetricInfoList?.forEach((el) => {
        temList.push({
          id: item.Product,
          productName: item.ProductName,
          metricInfo: el,
        });
      });
    });
    setContentTabs(temTabs);
    setTabsDataMap(temMap);
    setList(temList);
  };
  useEffect(() => {
    if (visible) {
      getDescribeGuardAISubscribeMetricInfo();
    }
  }, [visible]);
  useEffect(() => {
    getDescribeGuardAISubscribeMetricInfo();
  }, []);

  return <ChatDrawer
    className={'sub-config-drawer-wrap'}
    visible={visible}
    destroyOnClose={false}
    outerClickClosable
    placement='bottom'
    onClose={() => onClose?.()}
    title={
      <div className={'search-wrap'}>
        <Icon type="search" />
        <Input
          value={searchValue}
          onChange={
            (val) => {
              setSearchValue(val);
            }
          }
          size={'full'}
        />
        {
          searchValue && <div
            className={'close-icon-wrap'}
            onClick={
              () => {
                setSearchValue('');
              }
            }
          >
            <Icon type="close" />
          </div>
        }
      </div>
    }
  >
    {searchValue ? (
      <div>
        <ChatInstructionList>
          {list
            .filter(item => item?.metricInfo?.MetricName.includes(searchValue))
            .map((item, index) => <Bubble content={item?.metricInfo.MetricName ?? ''} key={index}>
                <ChatInstructionList.Item
                  keyword={searchValue}
                  label={item?.metricInfo.MetricName ?? ''}
                  category={item.productName ?? ''}
                  showCollect={false}
                  onClick={
                    () => {
                      setInputValue(`${inputValue}${inputValue && '\n'}${item.id} ${item?.metricInfo.MetricName}`);
                      onClose?.();
                    }
                  }
                />
              </Bubble>)}
        </ChatInstructionList>
      </div>
    ) : (
      <ChatTabs tabs={titleTabs} className={'title-tabs-wrap'}>
        <ChatTabs.TabPanel id={TitleTabsEnum.template}>
          {
            contentTabs?.length > 0 ? <ChatTabs placement='left' tabs={contentTabs}>
              {
                contentTabs?.map((item, i) => <ChatTabs.TabPanel id={item.id} key={i}>
                  <ChatInstructionList>
                    {tabsDataMap?.[item.id]?.map((el, index) => (
                      <ChatInstructionList.Item
                        label={el.MetricName}
                        key={index}
                        showCollect={false}
                        onClick={
                          () => {
                            setInputValue(`${inputValue}${inputValue && '\n'}${item.id} ${el.MetricName}`);
                            onClose?.();
                          }
                        }
                      />
                    ))}
                  </ChatInstructionList>
                </ChatTabs.TabPanel>)
              }
            </ChatTabs> : <div className="status-wrap">
              <StatusTip status={'empty'} />
            </div>
          }
        </ChatTabs.TabPanel>
      </ChatTabs>
    )}
  </ChatDrawer>;
};

export default SubscribeConfigBotDrawer;
